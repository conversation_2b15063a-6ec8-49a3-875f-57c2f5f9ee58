import {
  MoreVert<PERSON>,
  Share,
  <PERSON>cil,
  Trash2,
  Fold<PERSON><PERSON><PERSON>,
  Download,
  FileX,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Link } from "@/components/Common/Link";
import { useState, useCallback, useEffect, useRef } from "react";
import { formatDuration, formatCreatedTime } from "@/lib/utils";
import { formatDateWithTimezone } from "@/components/Dashboard/Settings/TimezoneUtils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { StatusIcon } from "@/components/Common/StatusIcon";
import { FILE_STATUS, FILE_STATUS_UTILS } from "@/constants/file";
import ShareDialog from "@/components/Dashboard/ShareDialog";
import RenameDialog from "@/components/Dashboard/RenameDialog";
import DeleteDialog from "@/components/Dashboard/DeleteDialog";
import DeleteOriginalFileDialog from "@/components/Dashboard/DeleteOriginalFileDialog";
import MoveToFolderDialog from "./MoveToFolderDialog";
import ExportDialog from "./ExportDialog";
import { transcriptionService } from "@/services/api/transcriptionService";
import { folderService } from "@/services/api/folderService";
import { Pagination } from "./Pagination";
import { TableSkeleton } from "./TableSkeleton";
import { useTranslations, useLocale } from "next-intl";
import { toast } from "sonner";

export function FileTable({
  renderEmpty,
  renderHeader,
  searchKeyword,
  selectedFolderId = "all",
  selectedFiles = new Map(),
  onFileSelectionChange,
  isBatchMode = false,
  refreshTrigger = 0,
  folders = [],
}) {
  const t = useTranslations("dashboard.fileList");
  const locale = useLocale();
  const [files, setFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 10;
  const isResettingPageRef = useRef(false);

  // 根据文件夹ID获取文件夹名称
  const getFolderName = useCallback(
    (folderId) => {
      if (!folderId) {
        return t("table.columns.uncategorized");
      }
      const folder = folders.find(
        (f) => f.id === folderId || f.id === String(folderId)
      );
      return folder ? folder.name : t("table.columns.uncategorized");
    },
    [folders, t]
  );

  // 判断是否应该显示文件夹列
  const shouldShowFolderColumn = isBatchMode && selectedFolderId === "all";

  // 文本截断组件，带hover提示
  const TruncatedText = ({ text, className = "" }) => {
    const displayText = text || "-";

    // 如果文本很短，不需要tooltip
    if (displayText.length <= 20) {
      return <div className={`truncate ${className}`}>{displayText}</div>;
    }

    return (
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <div className={`truncate cursor-help ${className}`}>
              {displayText}
            </div>
          </TooltipTrigger>
          <TooltipContent side="top" className="max-w-sm">
            <p className="break-words">{displayText}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  // 时间显示组件，始终显示hover提示
  const TimeDisplay = ({ text, className = "" }) => {
    const displayText = text || "-";

    // 对于时间显示，始终提供tooltip以显示完整时间
    return (
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <div className={`truncate cursor-help ${className}`}>
              {displayText}
            </div>
          </TooltipTrigger>
          <TooltipContent side="top" className="max-w-sm">
            <p className="break-words">{displayText}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  const fetchFiles = async () => {
    try {
      setIsLoading(true);
      let response;

      if (searchKeyword) {
        // 在当前选中的文件夹下进行搜索
        response = await transcriptionService.searchTranscriptions(
          searchKeyword,
          currentPage,
          itemsPerPage,
          selectedFolderId
        );
      } else if (selectedFolderId === "all") {
        // "All Files"文件夹（Home状态），获取所有转录记录
        response = await transcriptionService.getTranscriptionsByPage(
          currentPage,
          itemsPerPage
        );
      } else if (selectedFolderId === "unclassified") {
        // "Uncategorized"文件夹，获取未分类的转录记录
        response = await transcriptionService.getTranscriptionsByPage(
          currentPage,
          itemsPerPage,
          "unclassified"
        );
      } else {
        // 特定文件夹，获取该文件夹下的转录记录
        response = await folderService.getFolderTranscriptions(
          selectedFolderId,
          currentPage,
          itemsPerPage
        );
      }

      const { items, total } = response.data;
      setFiles(items);
      setTotalPages(Math.ceil(total / itemsPerPage));
    } catch (error) {
      console.error("Error fetching files:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // 当搜索关键词或选中文件夹变化时，重置到第一页并获取数据
  useEffect(() => {
    isResettingPageRef.current = true;
    setCurrentPage(1);
    fetchFiles();
  }, [searchKeyword, selectedFolderId]);

  // 当页码变化时获取数据（但不包括由上面 useEffect 触发的页码重置）
  useEffect(() => {
    if (isResettingPageRef.current) {
      isResettingPageRef.current = false;
      return;
    }
    fetchFiles();
  }, [currentPage]);

  // 当refreshTrigger变化时，刷新数据
  useEffect(() => {
    if (refreshTrigger > 0) {
      fetchFiles();
    }
  }, [refreshTrigger]);

  const handleDelete = async (id) => {
    try {
      await transcriptionService.deleteTranscription(id);
      // 显示成功提示
      toast.success(t("deleteSuccess"));
      // 重新获取当前页数据
      fetchFiles();
    } catch (error) {
      console.error("Error deleting transcription:", error);
      // 显示错误提示
      toast.error(t("deleteError"));
    }
  };

  const handleUpdate = async (updatedFile) => {
    setFiles((prev) =>
      prev.map((file) => (file.id === updatedFile.id ? updatedFile : file))
    );
  };

  const handleMoveSuccess = (transcriptionId, folderId) => {
    // 如果当前在特定文件夹中，且文件被移出了该文件夹，则从列表中移除
    if (selectedFolderId !== "all") {
      setFiles((prevFiles) =>
        prevFiles.filter((file) => file.id !== transcriptionId)
      );
    } else {
      // 如果在"All Files"中，更新文件的文件夹信息
      setFiles((prevFiles) =>
        prevFiles.map((file) =>
          file.id === transcriptionId
            ? {
                ...file,
                folderId,
                folderName: folderId ? "Updated Folder" : null,
              }
            : file
        )
      );
    }
  };

  // 文件选择处理函数
  const handleFileSelect = (fileId, checked) => {
    if (!onFileSelectionChange) return;

    const newSelection = new Map(selectedFiles);
    if (checked) {
      // 找到对应的文件对象并存储
      const file = files.find((f) => f.id === fileId);
      if (file) {
        newSelection.set(String(fileId), file);
      }
    } else {
      newSelection.delete(String(fileId));
    }
    onFileSelectionChange(newSelection);
  };

  const handleSelectAll = (checked) => {
    if (!onFileSelectionChange) return;

    if (checked) {
      // 全选当前页面：将当前页面的文件添加到现有选择中
      const newSelection = new Map(selectedFiles);
      files.forEach((file) => {
        newSelection.set(String(file.id), file);
      });
      onFileSelectionChange(newSelection);
    } else {
      // 取消全选当前页面：从现有选择中移除当前页面的文件ID
      const newSelection = new Map(selectedFiles);
      files.forEach((file) => {
        newSelection.delete(String(file.id));
      });
      onFileSelectionChange(newSelection);
    }
  };

  // 检查是否全选
  const isAllSelected =
    files.length > 0 &&
    files.every((file) => selectedFiles.has(String(file.id)));
  const isIndeterminate =
    files.some((file) => selectedFiles.has(String(file.id))) && !isAllSelected;

  if (isLoading && files.length === 0) {
    return <TableSkeleton />;
  }

  if (files.length === 0) {
    if (searchKeyword) {
      return (
        <div className="flex flex-col h-full">
          {renderHeader?.()}
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <p className="text-gray-500">
                {t("noResults.title", { keyword: searchKeyword })}
              </p>
              <p className="text-sm text-gray-400 mt-1">
                {t("noResults.subtitle")}
              </p>
            </div>
          </div>
        </div>
      );
    }
    return renderEmpty?.();
  }

  return (
    <div className="flex flex-col h-full">
      {renderHeader?.()}
      <div className="flex-1 overflow-auto">
        <div className="overflow-hidden">
          <div className="grid grid-cols-12 gap-4 py-4 px-6 bg-gray-50 font-medium text-sm">
            {isBatchMode && (
              <div className="col-span-1">
                <Checkbox
                  checked={isAllSelected}
                  indeterminate={isIndeterminate}
                  onCheckedChange={handleSelectAll}
                  className="data-[state=checked]:bg-custom-bg data-[state=checked]:border-custom-bg"
                />
              </div>
            )}
            <div
              className={`${
                isBatchMode
                  ? shouldShowFolderColumn
                    ? "col-span-5"
                    : "col-span-5"
                  : shouldShowFolderColumn
                  ? "col-span-5"
                  : "col-span-5"
              } text-left`}
            >
              {t("table.columns.name")}
            </div>
            <div
              className={`${
                shouldShowFolderColumn ? "col-span-1" : "col-span-1"
              } text-left`}
            >
              {t("table.columns.duration")}
            </div>
            <div
              className={`${
                shouldShowFolderColumn ? "col-span-2" : "col-span-3"
              } text-left`}
            >
              {t("table.columns.created")}
            </div>
            <div
              className={`${
                shouldShowFolderColumn ? "col-span-1" : "col-span-2"
              } text-left`}
            >
              {t("table.columns.type")}
            </div>
            {shouldShowFolderColumn && (
              <div className="col-span-2 text-left">
                {t("table.columns.folder")}
              </div>
            )}
            {!isBatchMode && <div className="col-span-1"></div>}
          </div>
          {files.map((file) => (
            <div
              key={file.id}
              className="grid grid-cols-12 gap-4 py-4 px-6 hover:bg-gray-50 items-center min-h-[60px]"
            >
              {isBatchMode && (
                <div className="col-span-1">
                  <Checkbox
                    checked={selectedFiles.has(String(file.id))}
                    onCheckedChange={(checked) =>
                      handleFileSelect(file.id, checked)
                    }
                    className="data-[state=checked]:bg-custom-bg data-[state=checked]:border-custom-bg"
                  />
                </div>
              )}
              <div
                className={`${
                  isBatchMode
                    ? shouldShowFolderColumn
                      ? "col-span-5"
                      : "col-span-5"
                    : shouldShowFolderColumn
                    ? "col-span-5"
                    : "col-span-5"
                } text-left flex items-center gap-2`}
              >
                <StatusIcon status={file.status} />
                <TooltipProvider>
                  <Tooltip delayDuration={300}>
                    <TooltipTrigger asChild>
                      <div className="truncate cursor-help flex-1">
                        <Link
                          href={`/transcriptions/${file.id}`}
                          className="text-sm font-medium truncate block hover:text-custom-bg transition-colors cursor-pointer"
                        >
                          {file.filename}
                        </Link>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="max-w-sm">
                      <p className="break-words">{file.filename}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <div
                className={`${
                  shouldShowFolderColumn ? "col-span-1" : "col-span-1"
                } text-sm text-gray-500 text-left`}
              >
                <TruncatedText
                  text={formatDuration(file.duration)}
                  className="text-gray-500"
                />
              </div>
              <div
                className={`${
                  shouldShowFolderColumn ? "col-span-2" : "col-span-3"
                } text-sm text-gray-500 text-left`}
              >
                <TimeDisplay
                  text={formatCreatedTime(file.createdTime, locale)}
                  className="text-gray-500"
                />
              </div>
              <div
                className={`${
                  shouldShowFolderColumn ? "col-span-1" : "col-span-2"
                } text-sm text-gray-500 text-left`}
              >
                <TruncatedText
                  text={t(
                    `transcriptionTypes.${file.transcriptionType.toLowerCase()}`,
                    { defaultValue: file.transcriptionType }
                  )}
                  className="text-gray-500"
                />
              </div>
              {shouldShowFolderColumn && (
                <div className="col-span-2 text-sm text-gray-500 text-left">
                  <TruncatedText
                    text={getFolderName(file.folderId)}
                    className="text-gray-500"
                  />
                </div>
              )}
              {!isBatchMode && (
                <div className="col-span-1">
                  <FileActions
                    file={file}
                    onDelete={handleDelete}
                    updateTranscription={handleUpdate}
                    onMoveSuccess={handleMoveSuccess}
                  />
                </div>
              )}
            </div>
          ))}
        </div>

        {isLoading && files.length > 0 && (
          <div className="flex justify-center my-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-custom-bg-600" />
          </div>
        )}
      </div>

      {!isLoading && files.length > 0 && totalPages > 1 && (
        <div className="mt-auto">
          <div className="mt-8 flex justify-center">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </div>
        </div>
      )}
    </div>
  );
}

function FileActions({ file, onDelete, updateTranscription, onMoveSuccess }) {
  const t = useTranslations("dashboard.fileList.table.actions");
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleteOriginalFileDialogOpen, setIsDeleteOriginalFileDialogOpen] =
    useState(false);
  const [isMoveDialogOpen, setIsMoveDialogOpen] = useState(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // 检查是否为完成状态（用于分享、导出、删除原文件等功能）
  const isTranscriptionCompleted = [
    FILE_STATUS.COMPLETED,
    FILE_STATUS.COMPLETED_WITH_ERRORS,
    FILE_STATUS.PARTIALLY_COMPLETED,
  ].includes(file.status);

  const canDelete = FILE_STATUS_UTILS.canDelete(file.status);

  // 检查是否可以删除原文件（转录完成且原文件未被删除）
  const canDeleteOriginalFile =
    isTranscriptionCompleted && !file.originalFileDeleted;

  const handleShare = (event) => {
    event.preventDefault();
    setIsShareDialogOpen(true);
    setIsDropdownOpen(false);
  };

  const handleExport = (event) => {
    event.preventDefault();
    setIsExportDialogOpen(true);
    setIsDropdownOpen(false);
  };

  const handleRename = (event) => {
    event.preventDefault();
    setIsRenameDialogOpen(true);
    setIsDropdownOpen(false);
  };

  const handleRenameSubmit = async (newFilename) => {
    try {
      await transcriptionService.renameTranscription(file.id, newFilename);
      const updatedFile = { ...file, filename: newFilename };
      updateTranscription(updatedFile);
    } catch (error) {
      console.error("Error renaming transcription:", error);
    }
  };

  const handleMove = (event) => {
    event.preventDefault();
    setIsMoveDialogOpen(true);
    setIsDropdownOpen(false);
  };

  const handleMoveSuccess = (transcriptionId, folderId) => {
    // 通知父组件移动成功
    if (onMoveSuccess) {
      onMoveSuccess(transcriptionId, folderId);
    }
  };

  const handleDelete = (event) => {
    event.preventDefault();
    setIsDeleteDialogOpen(true);
    setIsDropdownOpen(false);
  };

  const handleDeleteOriginalFile = (event) => {
    event.preventDefault();
    setIsDeleteOriginalFileDialogOpen(true);
    setIsDropdownOpen(false);
  };

  const handleDeleteSubmit = async () => {
    try {
      await onDelete(file.id);
      setIsDeleteDialogOpen(false);
      setIsDropdownOpen(false);
    } catch (error) {
      console.error("Error deleting transcription:", error);
      // 错误提示已在handleDelete中处理，这里不需要重复显示
    }
  };

  const handleDeleteOriginalFileSubmit = async () => {
    try {
      await transcriptionService.deleteOriginalFile(file.id);
      // 显示成功提示
      toast.success(t("deleteOriginalFileSuccess"));

      // 更新文件状态，标记原文件已删除
      const updatedFile = {
        ...file,
        originalFileDeleted: true,
        canPlay: false,
      };
      updateTranscription(updatedFile);

      setIsDeleteOriginalFileDialogOpen(false);
      setIsDropdownOpen(false);
    } catch (error) {
      console.error("Error deleting original file:", error);
      // 显示错误提示
      toast.error(t("deleteOriginalFileError"));
    }
  };

  return (
    <>
      <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm">
            <MoreVertical className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {isTranscriptionCompleted && (
            <DropdownMenuItem onSelect={handleShare}>
              <span className="flex items-center">
                <Share className="mr-2 h-4 w-4" />
                {t("share")}
              </span>
            </DropdownMenuItem>
          )}
          {isTranscriptionCompleted && (
            <DropdownMenuItem onSelect={handleExport}>
              <span className="flex items-center">
                <Download className="mr-2 h-4 w-4" />
                {t("export")}
              </span>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem onSelect={handleRename}>
            <span className="flex items-center">
              <Pencil className="mr-2 h-4 w-4" />
              {t("rename")}
            </span>
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={handleMove}>
            <span className="flex items-center">
              <FolderOpen className="mr-2 h-4 w-4" />
              {t("move")}
            </span>
          </DropdownMenuItem>
          {canDelete && (
            <DropdownMenuItem onSelect={handleDelete} className="text-red-600">
              <span className="flex items-center">
                <Trash2 className="mr-2 h-4 w-4" />
                {t("delete")}
              </span>
            </DropdownMenuItem>
          )}
          {canDeleteOriginalFile && (
            <DropdownMenuItem
              onSelect={handleDeleteOriginalFile}
              className="text-red-600"
            >
              <span className="flex items-center">
                <FileX className="mr-2 h-4 w-4" />
                {t("deleteOriginalFile")}
              </span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <ShareDialog
        fileId={file.id}
        isOpen={isShareDialogOpen}
        onOpenChange={(open) => {
          setIsShareDialogOpen(open);
          if (!open) setIsDropdownOpen(false);
        }}
      />

      <RenameDialog
        isOpen={isRenameDialogOpen}
        onOpenChange={(open) => {
          setIsRenameDialogOpen(open);
          if (!open) setIsDropdownOpen(false);
        }}
        filename={file.filename}
        onRename={handleRenameSubmit}
      />

      <DeleteDialog
        isOpen={isDeleteDialogOpen}
        onOpenChange={(open) => {
          setIsDeleteDialogOpen(open);
          if (!open) setIsDropdownOpen(false);
        }}
        filename={file.filename}
        onDelete={handleDeleteSubmit}
      />

      <DeleteOriginalFileDialog
        isOpen={isDeleteOriginalFileDialogOpen}
        onOpenChange={(open) => {
          setIsDeleteOriginalFileDialogOpen(open);
          if (!open) setIsDropdownOpen(false);
        }}
        filename={file.filename}
        onDelete={handleDeleteOriginalFileSubmit}
      />

      <MoveToFolderDialog
        isOpen={isMoveDialogOpen}
        onClose={() => {
          setIsMoveDialogOpen(false);
          setIsDropdownOpen(false);
        }}
        transcription={file}
        onMoveSuccess={handleMoveSuccess}
      />

      <ExportDialog
        isOpen={isExportDialogOpen}
        onOpenChange={(open) => {
          setIsExportDialogOpen(open);
          if (!open) setIsDropdownOpen(false);
        }}
        fileId={file.id}
        transcriptionType={file.transcriptionType}
        insufficientMinutes={0}
        exportSource="filelist_action"
        onExportComplete={() => {
          setIsExportDialogOpen(false);
          setIsDropdownOpen(false);
        }}
      />
    </>
  );
}
